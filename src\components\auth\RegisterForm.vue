<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import { toFormValidator } from '@vee-validate/zod'
import { useAuthStore } from '@/stores/counter'
import { toast } from '@/composables/useToast'

const router = useRouter()
const authStore = useAuthStore()

const validationSchema = toFormValidator(
  z.object({
    fullName: z.string().min(2, 'Full name must be at least 2 characters'),
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters'),
    confirmPassword: z.string()
  }).refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword']
  })
)

const { handleSubmit, errors, defineField } = useForm({
  validationSchema
})

const [fullName] = defineField('fullName')
const [email] = defineField('email')
const [password] = defineField('password')
const [confirmPassword] = defineField('confirmPassword')

const isSubmitting = ref(false)

const onSubmit = handleSubmit(async (values) => {
  isSubmitting.value = true
  
  try {
    const { success, error } = await authStore.signUp(values.email, values.password, values.fullName)
    
    if (success) {
      toast.success('Registration successful', 'Your account has been created')
      router.push('/my-land')
    } else {
      toast.error('Registration failed', error || 'Please check your information and try again')
    }
  } catch (err: any) {
    toast.error('Registration failed', err.message || 'An unexpected error occurred')
  } finally {
    isSubmitting.value = false
  }
})
</script>

<template>
  <div class="w-full max-w-md mx-auto">
    <form @submit.prevent="onSubmit" class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <h2 class="text-2xl font-bold mb-6 text-center text-gray-800">Register</h2>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="fullName">
          Full Name
        </label>
        <input
          id="fullName"
          v-model="fullName"
          type="text"
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          :class="{ 'border-red-500': errors.fullName }"
          placeholder="Full Name"
        />
        <p v-if="errors.fullName" class="text-red-500 text-xs italic mt-1">{{ errors.fullName }}</p>
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
          Email
        </label>
        <input
          id="email"
          v-model="email"
          type="email"
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          :class="{ 'border-red-500': errors.email }"
          placeholder="Email"
        />
        <p v-if="errors.email" class="text-red-500 text-xs italic mt-1">{{ errors.email }}</p>
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="password">
          Password
        </label>
        <input
          id="password"
          v-model="password"
          type="password"
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          :class="{ 'border-red-500': errors.password }"
          placeholder="Password"
        />
        <p v-if="errors.password" class="text-red-500 text-xs italic mt-1">{{ errors.password }}</p>
      </div>
      
      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="confirmPassword">
          Confirm Password
        </label>
        <input
          id="confirmPassword"
          v-model="confirmPassword"
          type="password"
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          :class="{ 'border-red-500': errors.confirmPassword }"
          placeholder="Confirm Password"
        />
        <p v-if="errors.confirmPassword" class="text-red-500 text-xs italic mt-1">{{ errors.confirmPassword }}</p>
      </div>
      
      <div class="flex items-center justify-between">
        <button
          type="submit"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
          :disabled="isSubmitting"
        >
          <span v-if="isSubmitting">Registering...</span>
          <span v-else>Register</span>
        </button>
      </div>
      
      <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
          Already have an account?
          <router-link to="/login" class="text-blue-500 hover:text-blue-700">Login</router-link>
        </p>
      </div>
    </form>
  </div>
</template>
