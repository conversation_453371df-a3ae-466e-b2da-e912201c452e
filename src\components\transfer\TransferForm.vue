<script setup lang="ts">
import { ref, computed } from 'vue'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import { toFormValidator } from '@vee-validate/zod'
import { useTransferStore } from '@/stores/transfer'
import { useLandStore } from '@/stores/land'
import { useAuthStore } from '@/stores/counter'
import { toast } from '@/composables/useToast'

const transferStore = useTransferStore()
const landStore = useLandStore()
const authStore = useAuthStore()

const validationSchema = toFormValidator(
  z.object({
    parcel_id: z.string().min(3, 'Parcel ID must be at least 3 characters'),
    recipient_id: z.string().min(3, 'Recipient ID must be at least 3 characters')
  })
)

const { handleSubmit, errors, defineField, resetForm } = useForm({
  validationSchema
})

const [parcel_id] = defineField('parcel_id')
const [recipient_id] = defineField('recipient_id')

const contractDocument = ref<File | null>(null)
const isSubmitting = ref(false)
const documentError = ref('')
const showModal = ref(false)
const formValues = ref<any>(null)

const userLands = computed(() => landStore.lands)

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    
    // Check file type
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      documentError.value = 'Only PDF, JPEG, and PNG files are allowed'
      contractDocument.value = null
      return
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      documentError.value = 'File size must be less than 5MB'
      contractDocument.value = null
      return
    }
    
    documentError.value = ''
    contractDocument.value = file
  }
}

const onSubmit = handleSubmit(async (values) => {
  if (!contractDocument.value) {
    documentError.value = 'Contract document is required'
    return
  }
  
  if (!authStore.user) {
    toast.error('Authentication error', 'You must be logged in to initiate a transfer')
    return
  }
  
  // Store values for confirmation
  formValues.value = { ...values }
  showModal.value = true
})

const confirmTransfer = async () => {
  if (!formValues.value || !contractDocument.value || !authStore.user) {
    showModal.value = false
    return
  }
  
  isSubmitting.value = true
  
  try {
    // Upload document first
    const { success: uploadSuccess, url, error: uploadError } = await transferStore.uploadContractDocument(
      contractDocument.value,
      authStore.user.id
    )
    
    if (!uploadSuccess || !url) {
      throw new Error(uploadError || 'Failed to upload document')
    }
    
    // Create transfer with document URL
    const { success, error } = await transferStore.createTransfer({
      parcel_id: formValues.value.parcel_id,
      sender_id: authStore.user.id,
      recipient_id: formValues.value.recipient_id,
      contract_document: url,
      status: 'Pending'
    })
    
    if (success) {
      toast.success('Transfer initiated', 'Your transfer request has been submitted successfully')
      resetForm()
      contractDocument.value = null
      showModal.value = false
    } else {
      toast.error('Transfer failed', error || 'Failed to initiate transfer')
    }
  } catch (err: any) {
    toast.error('Transfer failed', err.message || 'An unexpected error occurred')
  } finally {
    isSubmitting.value = false
    showModal.value = false
  }
}

const cancelTransfer = () => {
  showModal.value = false
}
</script>

<template>
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Initiate Land Transfer</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">
        Fill in the details to transfer land ownership
      </p>
    </div>
    
    <div class="border-t border-gray-200">
      <form @submit.prevent="onSubmit" class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-3">
            <label for="parcel_id" class="block text-sm font-medium text-gray-700">Parcel ID</label>
            <div class="mt-1">
              <select
                id="parcel_id"
                v-model="parcel_id"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :class="{ 'border-red-500': errors.parcel_id }"
              >
                <option value="">Select a parcel</option>
                <option v-for="land in userLands" :key="land.id" :value="land.parcel_id">
                  {{ land.parcel_id }} - {{ land.address }}
                </option>
              </select>
              <p v-if="errors.parcel_id" class="mt-2 text-sm text-red-600">{{ errors.parcel_id }}</p>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label for="recipient_id" class="block text-sm font-medium text-gray-700">Recipient ID</label>
            <div class="mt-1">
              <input
                id="recipient_id"
                v-model="recipient_id"
                type="text"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :class="{ 'border-red-500': errors.recipient_id }"
                placeholder="Enter recipient's user ID"
              />
              <p v-if="errors.recipient_id" class="mt-2 text-sm text-red-600">{{ errors.recipient_id }}</p>
            </div>
          </div>

          <div class="sm:col-span-6">
            <label for="contract_document" class="block text-sm font-medium text-gray-700">
              Contract Document (PDF, JPEG, PNG)
            </label>
            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div class="space-y-1 text-center">
                <svg
                  class="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                  aria-hidden="true"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <div class="flex text-sm text-gray-600">
                  <label
                    for="file-upload"
                    class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                  >
                    <span>Upload a file</span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      class="sr-only"
                      accept=".pdf,.jpg,.jpeg,.png"
                      @change="handleFileChange"
                    />
                  </label>
                  <p class="pl-1">or drag and drop</p>
                </div>
                <p class="text-xs text-gray-500">PDF, PNG, JPG up to 5MB</p>
                <p v-if="contractDocument" class="text-sm text-green-600">
                  Selected: {{ contractDocument.name }}
                </p>
              </div>
            </div>
            <p v-if="documentError" class="mt-2 text-sm text-red-600">{{ documentError }}</p>
          </div>
        </div>

        <div class="pt-5">
          <div class="flex justify-end">
            <button
              type="button"
              class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              @click="resetForm"
              :disabled="isSubmitting"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting">Submitting...</span>
              <span v-else>Initiate Transfer</span>
            </button>
          </div>
        </div>
      </form>
    </div>
    
    <!-- Confirmation Modal -->
    <div v-if="showModal" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
          <div>
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100">
              <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="mt-3 text-center sm:mt-5">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                Confirm Land Transfer
              </h3>
              <div class="mt-2">
                <p class="text-sm text-gray-500">
                  Are you sure you want to transfer the land with Parcel ID <strong>{{ formValues?.parcel_id }}</strong> to recipient with ID <strong>{{ formValues?.recipient_id }}</strong>? This action cannot be undone.
                </p>
              </div>
            </div>
          </div>
          <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button
              type="button"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:col-start-2 sm:text-sm"
              @click="confirmTransfer"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting">Processing...</span>
              <span v-else>Confirm Transfer</span>
            </button>
            <button
              type="button"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:col-start-1 sm:text-sm"
              @click="cancelTransfer"
              :disabled="isSubmitting"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
