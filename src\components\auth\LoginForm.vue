<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import { toFormValidator } from '@vee-validate/zod'
import { useAuthStore } from '@/stores/counter'
import { toast } from '@/composables/useToast'

const router = useRouter()
const authStore = useAuthStore()

const validationSchema = toFormValidator(
  z.object({
    email: z.string().email('Please enter a valid email address'),
    password: z.string().min(6, 'Password must be at least 6 characters')
  })
)

const { handleSubmit, errors, resetForm, defineField } = useForm({
  validationSchema
})

const [email] = defineField('email')
const [password] = defineField('password')

const isSubmitting = ref(false)

const onSubmit = handleSubmit(async (values) => {
  isSubmitting.value = true
  
  try {
    const { success, error } = await authStore.signIn(values.email, values.password)
    
    if (success) {
      toast.success('Login successful', 'Welcome back!')
      router.push('/my-land')
    } else {
      toast.error('Login failed', error || 'Please check your credentials and try again')
    }
  } catch (err: any) {
    toast.error('Login failed', err.message || 'An unexpected error occurred')
  } finally {
    isSubmitting.value = false
  }
})
</script>

<template>
  <div class="w-full max-w-md mx-auto">
    <form @submit.prevent="onSubmit" class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <h2 class="text-2xl font-bold mb-6 text-center text-gray-800">Login</h2>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
          Email
        </label>
        <input
          id="email"
          v-model="email"
          type="email"
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          :class="{ 'border-red-500': errors.email }"
          placeholder="Email"
        />
        <p v-if="errors.email" class="text-red-500 text-xs italic mt-1">{{ errors.email }}</p>
      </div>
      
      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="password">
          Password
        </label>
        <input
          id="password"
          v-model="password"
          type="password"
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          :class="{ 'border-red-500': errors.password }"
          placeholder="Password"
        />
        <p v-if="errors.password" class="text-red-500 text-xs italic mt-1">{{ errors.password }}</p>
      </div>
      
      <div class="flex items-center justify-between">
        <button
          type="submit"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline w-full"
          :disabled="isSubmitting"
        >
          <span v-if="isSubmitting">Logging in...</span>
          <span v-else>Login</span>
        </button>
      </div>
      
      <div class="text-center mt-4">
        <p class="text-sm text-gray-600">
          Don't have an account?
          <router-link to="/register" class="text-blue-500 hover:text-blue-700">Register</router-link>
        </p>
      </div>
    </form>
  </div>
</template>
