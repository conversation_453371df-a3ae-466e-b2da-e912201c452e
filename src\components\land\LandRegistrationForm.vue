<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from 'vee-validate'
import { z } from 'zod'
import { toFormValidator } from '@vee-validate/zod'
import { useLandStore } from '@/stores/land'
import { useAuthStore } from '@/stores/counter'
import { toast } from '@/composables/useToast'

const landStore = useLandStore()
const authStore = useAuthStore()

const validationSchema = toFormValidator(
  z.object({
    parcel_id: z.string().min(3, 'Parcel ID must be at least 3 characters'),
    address: z.string().min(5, 'Address must be at least 5 characters'),
    land_size: z.number().positive('Land size must be a positive number'),
    ownership_type: z.string().min(1, 'Ownership type is required')
  })
)

const { handleSubmit, errors, defineField, resetForm } = useForm({
  validationSchema
})

const [parcel_id] = defineField('parcel_id')
const [address] = defineField('address')
const [land_size, land_sizeProps] = defineField('land_size', {
  initialValue: 0,
  validateOnValueUpdate: false
})
const [ownership_type] = defineField('ownership_type')

const proofDocument = ref<File | null>(null)
const isSubmitting = ref(false)
const documentError = ref('')

const handleFileChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    const file = target.files[0]
    
    // Check file type
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png']
    if (!allowedTypes.includes(file.type)) {
      documentError.value = 'Only PDF, JPEG, and PNG files are allowed'
      proofDocument.value = null
      return
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      documentError.value = 'File size must be less than 5MB'
      proofDocument.value = null
      return
    }
    
    documentError.value = ''
    proofDocument.value = file
  }
}

const onSubmit = handleSubmit(async (values) => {
  if (!proofDocument.value) {
    documentError.value = 'Proof of ownership document is required'
    return
  }
  
  if (!authStore.user) {
    toast.error('Authentication error', 'You must be logged in to register land')
    return
  }
  
  isSubmitting.value = true
  
  try {
    // Upload document first
    const { success: uploadSuccess, url, error: uploadError } = await landStore.uploadProofDocument(
      proofDocument.value,
      authStore.user.id
    )
    
    if (!uploadSuccess || !url) {
      throw new Error(uploadError || 'Failed to upload document')
    }
    
    // Register land with document URL
    const { success, error } = await landStore.registerLand({
      parcel_id: values.parcel_id,
      address: values.address,
      land_size: values.land_size,
      ownership_type: values.ownership_type,
      proof_document: url,
      status: 'Pending',
      owner_id: authStore.user.id
    })
    
    if (success) {
      toast.success('Land registration submitted', 'Your application has been submitted successfully')
      resetForm()
      proofDocument.value = null
    } else {
      toast.error('Registration failed', error || 'Failed to register land')
    }
  } catch (err: any) {
    toast.error('Registration failed', err.message || 'An unexpected error occurred')
  } finally {
    isSubmitting.value = false
  }
})
</script>

<template>
  <div class="bg-white shadow overflow-hidden sm:rounded-lg">
    <div class="px-4 py-5 sm:px-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Register New Land</h3>
      <p class="mt-1 max-w-2xl text-sm text-gray-500">
        Fill in the details to register your land
      </p>
    </div>
    
    <div class="border-t border-gray-200">
      <form @submit.prevent="onSubmit" class="px-4 py-5 sm:p-6">
        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
          <div class="sm:col-span-3">
            <label for="parcel_id" class="block text-sm font-medium text-gray-700">Parcel ID</label>
            <div class="mt-1">
              <input
                id="parcel_id"
                v-model="parcel_id"
                type="text"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :class="{ 'border-red-500': errors.parcel_id }"
              />
              <p v-if="errors.parcel_id" class="mt-2 text-sm text-red-600">{{ errors.parcel_id }}</p>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label for="land_size" class="block text-sm font-medium text-gray-700">Land Size (sq meters)</label>
            <div class="mt-1">
              <input
                id="land_size"
                v-model="land_size"
                type="number"
                min="0"
                step="0.01"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :class="{ 'border-red-500': errors.land_size }"
              />
              <p v-if="errors.land_size" class="mt-2 text-sm text-red-600">{{ errors.land_size }}</p>
            </div>
          </div>

          <div class="sm:col-span-6">
            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
            <div class="mt-1">
              <input
                id="address"
                v-model="address"
                type="text"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :class="{ 'border-red-500': errors.address }"
              />
              <p v-if="errors.address" class="mt-2 text-sm text-red-600">{{ errors.address }}</p>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label for="ownership_type" class="block text-sm font-medium text-gray-700">Ownership Type</label>
            <div class="mt-1">
              <select
                id="ownership_type"
                v-model="ownership_type"
                class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                :class="{ 'border-red-500': errors.ownership_type }"
              >
                <option value="">Select ownership type</option>
                <option value="Individual">Individual</option>
                <option value="Joint">Joint</option>
                <option value="Corporate">Corporate</option>
                <option value="Trust">Trust</option>
              </select>
              <p v-if="errors.ownership_type" class="mt-2 text-sm text-red-600">{{ errors.ownership_type }}</p>
            </div>
          </div>

          <div class="sm:col-span-6">
            <label for="proof_document" class="block text-sm font-medium text-gray-700">
              Proof of Ownership (PDF, JPEG, PNG)
            </label>
            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div class="space-y-1 text-center">
                <svg
                  class="mx-auto h-12 w-12 text-gray-400"
                  stroke="currentColor"
                  fill="none"
                  viewBox="0 0 48 48"
                  aria-hidden="true"
                >
                  <path
                    d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
                <div class="flex text-sm text-gray-600">
                  <label
                    for="file-upload"
                    class="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                  >
                    <span>Upload a file</span>
                    <input
                      id="file-upload"
                      name="file-upload"
                      type="file"
                      class="sr-only"
                      accept=".pdf,.jpg,.jpeg,.png"
                      @change="handleFileChange"
                    />
                  </label>
                  <p class="pl-1">or drag and drop</p>
                </div>
                <p class="text-xs text-gray-500">PDF, PNG, JPG up to 5MB</p>
                <p v-if="proofDocument" class="text-sm text-green-600">
                  Selected: {{ proofDocument.name }}
                </p>
              </div>
            </div>
            <p v-if="documentError" class="mt-2 text-sm text-red-600">{{ documentError }}</p>
          </div>
        </div>

        <div class="pt-5">
          <div class="flex justify-end">
            <button
              type="button"
              class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              @click="resetForm"
              :disabled="isSubmitting"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              :disabled="isSubmitting"
            >
              <span v-if="isSubmitting">Submitting...</span>
              <span v-else>Submit</span>
            </button>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>
